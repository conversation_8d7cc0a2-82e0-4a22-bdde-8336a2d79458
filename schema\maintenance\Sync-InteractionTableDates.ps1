# PowerShell script to synchronize interaction table dates across different database types
# This script executes the appropriate SQL synchronization script based on the database type
# Uses detailedinteractiondata as the authoritative source for sync dates

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("PostgreSQL", "MSSQL", "Snowflake")]
    [string]$DatabaseType,
    
    [Parameter(Mandatory=$true)]
    [string]$ConnectionString,
    
    [Parameter(Mandatory=$false)]
    [switch]$WhatIf,
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose
)

# Function to write colored output
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# Function to execute SQL script
function Invoke-SqlScript {
    param(
        [string]$ScriptPath,
        [string]$ConnectionString,
        [string]$DatabaseType
    )
    
    try {
        $scriptContent = Get-Content -Path $ScriptPath -Raw
        
        if ($WhatIf) {
            Write-ColorOutput "WHAT-IF: Would execute the following script:" "Yellow"
            Write-ColorOutput $ScriptPath "Cyan"
            Write-ColorOutput "Script content preview (first 500 characters):" "Yellow"
            Write-ColorOutput $scriptContent.Substring(0, [Math]::Min(500, $scriptContent.Length)) "Gray"
            return $true
        }
        
        switch ($DatabaseType) {
            "PostgreSQL" {
                # Requires Npgsql or similar PostgreSQL .NET provider
                Add-Type -Path "Npgsql.dll" -ErrorAction SilentlyContinue
                $connection = New-Object Npgsql.NpgsqlConnection($ConnectionString)
                $connection.Open()
                $command = $connection.CreateCommand()
                $command.CommandText = $scriptContent
                $result = $command.ExecuteNonQuery()
                $connection.Close()
                Write-ColorOutput "PostgreSQL script executed successfully. Rows affected: $result" "Green"
            }
            
            "MSSQL" {
                # Use SQL Server .NET provider
                $connection = New-Object System.Data.SqlClient.SqlConnection($ConnectionString)
                $connection.Open()
                $command = $connection.CreateCommand()
                $command.CommandText = $scriptContent
                $result = $command.ExecuteNonQuery()
                $connection.Close()
                Write-ColorOutput "MSSQL script executed successfully. Rows affected: $result" "Green"
            }
            
            "Snowflake" {
                Write-ColorOutput "For Snowflake, please execute the script manually using SnowSQL or Snowflake web interface:" "Yellow"
                Write-ColorOutput $ScriptPath "Cyan"
                Write-ColorOutput "Connection string: $ConnectionString" "Gray"
                return $false
            }
        }
        
        return $true
    }
    catch {
        Write-ColorOutput "Error executing script: $($_.Exception.Message)" "Red"
        return $false
    }
}

# Main execution
Write-ColorOutput "=== Genesys Adapter Interaction Table Date Synchronization ===" "Cyan"
Write-ColorOutput "Database Type: $DatabaseType" "White"
Write-ColorOutput "Connection String: $($ConnectionString.Substring(0, [Math]::Min(50, $ConnectionString.Length)))..." "Gray"

if ($WhatIf) {
    Write-ColorOutput "Running in WHAT-IF mode - no changes will be made" "Yellow"
}

# Determine script path based on database type
$scriptPath = switch ($DatabaseType) {
    "PostgreSQL" { "postgres/maintenance/sync_interaction_table_dates.sql" }
    "MSSQL" { "mssql/maintenance/sync_interaction_table_dates.sql" }
    "Snowflake" { "snowflake/maintenance/sync_interaction_table_dates.sql" }
}

$fullScriptPath = Join-Path $PSScriptRoot $scriptPath

# Verify script file exists
if (-not (Test-Path $fullScriptPath)) {
    Write-ColorOutput "Error: Script file not found at $fullScriptPath" "Red"
    exit 1
}

Write-ColorOutput "Using script: $fullScriptPath" "White"

# Confirm execution unless in WhatIf mode
if (-not $WhatIf) {
    $confirmation = Read-Host "This will modify the tabledefinitions table. Are you sure you want to continue? (y/N)"
    if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
        Write-ColorOutput "Operation cancelled by user." "Yellow"
        exit 0
    }
}

# Execute the script
Write-ColorOutput "Executing synchronization script..." "White"
$success = Invoke-SqlScript -ScriptPath $fullScriptPath -ConnectionString $ConnectionString -DatabaseType $DatabaseType

if ($success) {
    Write-ColorOutput "=== Synchronization completed successfully ===" "Green"
    Write-ColorOutput "All interaction tables now use the sync date from detailedinteractiondata" "White"
    Write-ColorOutput "Next steps:" "White"
    Write-ColorOutput "1. Verify the results by checking the tabledefinitions table" "Gray"
    Write-ColorOutput "2. Restart the Genesys Adapter services" "Gray"
    Write-ColorOutput "3. Monitor the next interaction job run" "Gray"
} else {
    Write-ColorOutput "=== Synchronization failed ===" "Red"
    Write-ColorOutput "Please check the error messages above and try again." "Gray"
    exit 1
}

# Example usage information
Write-ColorOutput "`nExample usage:" "Cyan"
Write-ColorOutput ".\Sync-InteractionTableDates.ps1 -DatabaseType PostgreSQL -ConnectionString 'Host=localhost;Database=genesys;Username=user;Password=****'" "Gray"
Write-ColorOutput ".\Sync-InteractionTableDates.ps1 -DatabaseType MSSQL -ConnectionString 'Server=localhost;Database=genesys;Integrated Security=true'" "Gray"
Write-ColorOutput ".\Sync-InteractionTableDates.ps1 -DatabaseType Snowflake -ConnectionString 'account=myaccount;user=myuser;****word=******;db=genesys' -WhatIf" "Gray"
