IF dbo.csg_table_exists('participantSummaryData') = 0
CREATE TABLE [participantSummaryData](
    [keyid] [nvarchar](100) NOT NULL,
    [conversationid] [nvarchar](50),
    [conversationstartdate] [datetime],
    [conversationstartdateltc] [datetime],
    [conversationenddate] [datetime],
    [conversationenddateltc] [datetime],
    [participantid] [nvarchar](50),
    [purpose] [nvarchar](50),
    [mediatype] [nvarchar](50),
    [userid] [nvarchar](50),
    [queueid] [nvarchar](50),
    [wrapupcode] [nvarchar](50),
    [wrapupnote] [text],
    [nflow] [int],
    [tivr] [decimal](20, 2),
    [tflow] [decimal](20, 2),
    [tflowdisconnect] [decimal](20, 2),
    [tflowexit] [decimal](20, 2),
    [tflowout] [decimal](20, 2),
    [tacd] [decimal](20, 2),
    [tacw] [decimal](20, 2),
    [talert] [decimal](20, 2),
    [tanswered] [decimal](20, 2),
    [ttalk] [decimal](20, 2),
    [ttalkcomplete] [decimal](20, 2),
    [thandle] [decimal](20, 2),
    [tconnected] [decimal](20, 2),
    [tfirstconnect] [decimal](20, 2),
    [tcontacting] [decimal](20, 2),
    [tdialing] [decimal](20, 2),
    [tfirstdial] [decimal](20, 2),
    [tnotresponding] [decimal](20, 2),
    [tabandon] [decimal](20, 2),
    [theld] [decimal](20, 2),
    [theldcomplete] [decimal](20, 2),
    [tvoicemail] [decimal](20, 2),
    [tmonitoring] [decimal](20, 2),
    [tmonitoringcomplete] [decimal](20, 2),
    [tshortabandon] [decimal](20, 2),
    [tagentresponsetime] [decimal](20, 2),
    [tuserResponseTime] [decimal](20, 2),
    [tActiveCallback] [decimal](20, 2),
    [tActiveCallbackComplete] [decimal](20, 2),
    [noffered] [int],
    [nconnected] [int],
    [nconsult] [int],
    [nconsulttransferred] [int],
    [ntransferred] [int],
    [nblindtransferred] [int],
    [nerror] [int],
    [noutbound] [int],
    [nstatetransitionerror] [int],
    [noversla] [int],
    [divisionid] [nvarchar](50),
    [divisionid2] [nvarchar](50),
    [divisionid3] [nvarchar](50),
    [nflowoutcome] [int],	
    [tflowoutcome] [decimal](20, 2),
    [nflowoutcomeFailed] [int],
    [nbotinteractions] [int],	
    [tPark] [decimal](20, 2),
    [tParkComplete] [decimal](20, 2),
    [noutboundattempted] [int],
    [noutboundconnected] [int],	
    [updated] [datetime],
    CONSTRAINT [PK_participantSummaryData] PRIMARY KEY ([keyid])
);

IF dbo.csg_index_exists('participantSummary_Conv', 'participantSummaryData') = 0
    CREATE INDEX [participantSummary_Conv] ON [participantSummaryData] ([conversationid]);

IF dbo.csg_index_exists('participantSummary_Queue', 'participantSummaryData') = 0
    CREATE INDEX [participantSummary_Queue] ON [participantSummaryData] ([queueid]);
    
IF dbo.csg_index_exists('participantSummary_User', 'participantSummaryData') = 0
    CREATE INDEX [participantSummary_User] ON [participantSummaryData] ([userid]);

IF dbo.csg_column_exists('participantSummaryData', 'tconnected') = 0
    ALTER TABLE participantSummaryData ADD tconnected DECIMAL(20, 2);
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN tconnected DECIMAL(20, 2);

IF dbo.csg_column_exists('participantSummaryData', 'tfirstconnect') = 0
    ALTER TABLE participantSummaryData ADD tfirstconnect DECIMAL(20, 2);
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN tfirstconnect DECIMAL(20, 2);

IF dbo.csg_column_exists('participantSummaryData', 'tfirstdial') = 0
    ALTER TABLE participantSummaryData ADD tfirstdial DECIMAL(20, 2);
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN tfirstdial DECIMAL(20, 2);

IF dbo.csg_column_exists('participantSummaryData', 'tmonitoringcomplete') = 0
    ALTER TABLE participantSummaryData ADD tmonitoringcomplete DECIMAL(20, 2);
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN tmonitoringcomplete DECIMAL(20, 2);

IF dbo.csg_column_exists('participantSummaryData', 'tuserResponseTime') = 0
    ALTER TABLE participantSummaryData ADD tuserResponseTime DECIMAL(20, 2);
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN tuserResponseTime DECIMAL(20, 2);

IF dbo.csg_column_exists('participantSummaryData', 'divisionid2') = 0
    ALTER TABLE participantSummaryData ADD divisionid2 NVARCHAR(50);
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN divisionid2 NVARCHAR(50);

IF dbo.csg_column_exists('participantSummaryData', 'divisionid3') = 0
    ALTER TABLE participantSummaryData ADD divisionid3 NVARCHAR(50);
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN divisionid3 NVARCHAR(50);

IF dbo.csg_column_exists('participantSummaryData', 'nflowoutcome') = 0
    ALTER TABLE participantSummaryData ADD nflowoutcome INT;
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN nflowoutcome INT;

IF dbo.csg_column_exists('participantSummaryData', 'tflowoutcome') = 0
    ALTER TABLE participantSummaryData ADD tflowoutcome DECIMAL(20, 2);
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN tflowoutcome DECIMAL(20, 2);

IF dbo.csg_column_exists('participantSummaryData', 'nflowoutcomeFailed') = 0
    ALTER TABLE participantSummaryData ADD nflowoutcomeFailed INT;
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN nflowoutcomeFailed INT;

IF dbo.csg_column_exists('participantSummaryData', 'noutboundattempted') = 0
    ALTER TABLE participantSummaryData ADD noutboundattempted INT;
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN noutboundattempted INT;

IF dbo.csg_column_exists('participantSummaryData', 'noutboundconnected') = 0
    ALTER TABLE participantSummaryData ADD noutboundconnected INT;
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN noutboundconnected INT;
IF dbo.csg_column_exists('participantSummaryData', 'wrapupnote') = 0
    ALTER TABLE participantSummaryData ADD wrapupnote TEXT;
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN wrapupnote TEXT;
IF dbo.csg_column_exists('participantSummaryData', 'tActiveCallback') = 0
    ALTER TABLE participantSummaryData ADD tActiveCallback DECIMAL(20, 2);
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN tActiveCallback DECIMAL(20, 2);
IF dbo.csg_column_exists('participantSummaryData', 'tActiveCallbackComplete') = 0
    ALTER TABLE participantSummaryData ADD tActiveCallbackComplete DECIMAL(20, 2);
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN tActiveCallbackComplete DECIMAL(20, 2);
IF dbo.csg_column_exists('participantSummaryData', 'conversationstartdate') = 0
    ALTER TABLE participantSummaryData ADD conversationstartdate datetime;
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN conversationstartdate datetime;
IF dbo.csg_column_exists('participantSummaryData', 'conversationstartdateltc') = 0
    ALTER TABLE participantSummaryData ADD conversationstartdateltc datetime;
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN conversationstartdateltc datetime;
IF dbo.csg_column_exists('participantSummaryData', 'conversationenddate') = 0
    ALTER TABLE participantSummaryData ADD conversationenddate datetime;
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN conversationenddate datetime;
IF dbo.csg_column_exists('participantSummaryData', 'conversationenddateltc') = 0
    ALTER TABLE participantSummaryData ADD conversationenddateltc datetime;
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN conversationenddateltc datetime;
IF dbo.csg_column_exists('participantSummaryData', 'nbotinteractions') = 0
    ALTER TABLE participantSummaryData ADD nbotinteractions INT;
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN nbotinteractions INT;
IF dbo.csg_column_exists('participantSummaryData', 'tPark') = 0
    ALTER TABLE participantSummaryData ADD tPark DECIMAL(20, 2);
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN tPark DECIMAL(20, 2);
IF dbo.csg_column_exists('participantSummaryData', 'tParkComplete') = 0
    ALTER TABLE participantSummaryData ADD tParkComplete DECIMAL(20, 2);
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN tParkComplete DECIMAL(20, 2);
IF dbo.csg_column_exists('participantSummaryData', 'nCobrowseSessions') = 0
    ALTER TABLE participantSummaryData ADD nCobrowseSessions INT;
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN nCobrowseSessions INT;

IF dbo.csg_column_exists('participantSummaryData', 'nOutboundAbandoned') = 0
    ALTER TABLE participantSummaryData ADD nOutboundAbandoned INT;
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN nOutboundAbandoned INT;

IF dbo.csg_column_exists('participantSummaryData', 'oMessageCount') = 0
    ALTER TABLE participantSummaryData ADD oMessageCount INT;
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN oMessageCount INT;

IF dbo.csg_column_exists('participantSummaryData', 'oMessageSegmentCount') = 0
    ALTER TABLE participantSummaryData ADD oMessageSegmentCount INT;
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN oMessageSegmentCount INT;

-- Add missing Participant-level fields
IF dbo.csg_column_exists('participantSummaryData', 'externalcontactid') = 0
    ALTER TABLE participantSummaryData ADD externalcontactid NVARCHAR(50);
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN externalcontactid NVARCHAR(50);

IF dbo.csg_column_exists('participantSummaryData', 'externalorganizationid') = 0
    ALTER TABLE participantSummaryData ADD externalorganizationid NVARCHAR(50);
ELSE
    ALTER TABLE participantSummaryData ALTER COLUMN externalorganizationid NVARCHAR(50);