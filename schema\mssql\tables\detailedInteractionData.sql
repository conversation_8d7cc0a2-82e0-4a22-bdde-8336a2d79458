IF dbo.csg_table_exists('detailedInteractionData') = 0
CREATE TABLE [detailedInteractionData](
    [keyid] [nvarchar](255) NOT NULL,
    [conversationid] [nvarchar](50),
    [conversationstartdate] [datetime],
    [conversationstartdateltc] [datetime],
    [conversationenddate] [datetime],
    [conversationenddateltc] [datetime],
    [conversationminmos] [decimal](20, 2),
    [conversationminrfactor] [decimal](20, 2),
    [externalTag] [nvarchar](50),
    [originaldirection] [nvarchar](50),
    [participantid] [nvarchar](50),
    [participantname] [nvarchar](255),
    [purpose] [nvarchar](50),
    [mediatype] [nvarchar](50),
    [ani] [nvarchar](400),
    [dnis] [nvarchar](400),
    [sessiondnis] [nvarchar](400),
    [edgeId] [nvarchar](50),
    [gencode] [nvarchar](50),
    [peer] [nvarchar](50),
    [remotedisplayable] [nvarchar](255),
    [segmentstartdate] [datetime],
    [segmentstartdateltc] [datetime],
    [segmentenddate] [datetime],
    [segmentenddateltc] [datetime],
    [segmenttime] [decimal](20, 2),
    [convtosegmentstarttime] [decimal](20, 2),
    [convtosegmentendtime] [decimal](20, 2),
    [segmenttype] [nvarchar](50),
    [conference] [bit],
    [disconnectiontype] [nvarchar](50),
    [wrapupcode] [nvarchar](255),
    [wrapupnote] [text],
    [recordingexists] [bit],
    [sessionprovider] [nvarchar](50),
    [flowid] [nvarchar](50),
    [flowname] [nvarchar](255),
    [flowversion] [decimal](20, 2),
    [flowtype] [nvarchar](50),
    [exitreason] [nvarchar](255),
    [entryreason] [nvarchar](255),
    [entrytype] [nvarchar](50),
    [transfertype] [nvarchar](50),
    [transfertargetname] [nvarchar](255),
    [queueid] [nvarchar](50),
    [userid] [nvarchar](50),
    [issuedcallback] [bit],
    [nflow] [int],
    [tivr] [decimal](20, 2),
    [tflow] [decimal](20, 2),
    [tflowdisconnect] [decimal](20, 2),
    [tflowexit] [decimal](20, 2),
    [tflowout] [decimal](20, 2),
    [tacd] [decimal](20, 2),
    [tacw] [decimal](20, 2),
    [talert] [decimal](20, 2),
    [tanswered] [decimal](20, 2),
    [tconnected] [decimal](20, 2),
    [tfirstconnect] [decimal](20, 2),
    [ttalk] [decimal](20, 2),
    [ttalkcomplete] [decimal](20, 2),
    [thandle] [decimal](20, 2),
    [tcontacting] [decimal](20, 2),
    [tdialing] [decimal](20, 2),
    [tfirstdial] [decimal](20, 2),
    [tnotresponding] [decimal](20, 2),
    [tabandon] [decimal](20, 2),
    [theld] [decimal](20, 2),
    [theldcomplete] [decimal](20, 2),
    [tvoicemail] [decimal](20, 2),
    [tmonitoring] [decimal](20, 2),
    [tmonitoringcomplete] [decimal](20, 2),
    [tshortabandon] [decimal](20, 2),
    [tagentresponsetime] [decimal](20, 2),
    [tActiveCallback] [decimal](20, 2),
    [tActiveCallbackComplete] [decimal](20, 2),
    [noffered] [int],
    [nconnected] [int],
    [nconsult] [int],
    [nconsulttransferred] [int],
    [ntransferred] [int],
    [nblindtransferred] [int],
    [nerror] [int],
    [noutbound] [int],
    [nstatetransitionerror] [int],
    [noversla] [int],
    [nflowoutcome] [int],
    [tflowoutcome] [decimal](20, 2),
    [nflowoutcomeFailed] [int],
    [nbotinteractions] [int],
    [tPark] [decimal](20, 2),
    [tParkComplete] [decimal](20, 2),
    [sessiondirection] [nvarchar](50),
    [segdestinationConversationId] [nvarchar](50),
    [tuserresponsetime] [decimal](20, 2),
    [noutboundattempted] [int], 
    [noutboundconnected] [int],
    [divisionid] [nvarchar](50),
    [divisionid2] [nvarchar](50),
    [divisionid3] [nvarchar](50),
    [updated] [datetime],
    CONSTRAINT [PK_detailedInteractionData] PRIMARY KEY ([keyid])
);

IF dbo.csg_index_exists('DetailedInteractionConv', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteractionConv] ON [detailedInteractionData] ([conversationid]);
IF dbo.csg_index_exists('DetailedInteractionConvEndUser', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteractionConvEndUser] ON [detailedInteractionData] ([conversationenddate], [userid]);
IF dbo.csg_index_exists('DetailedInteractionConvStartUser', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteractionConvStartUser] ON [detailedInteractionData] ([conversationstartdate], [userid]);
IF dbo.csg_index_exists('DetailedInteractionConvUser', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteractionConvUser] ON [detailedInteractionData] ([userid]);
IF dbo.csg_index_exists('DetailedInteractionDataConvEndLTCUser', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteractionDataConvEndLTCUser] ON [detailedInteractionData] ([conversationenddateltc], [userid]);
IF dbo.csg_index_exists('DetailedInteractionOriginalDir', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteractionOriginalDir] ON [detailedInteractionData] ([originaldirection]);
IF dbo.csg_index_exists('DetailedInteractionPurposeType', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteractionPurposeType] ON [detailedInteractionData] ([purpose], [segmenttype]);
IF dbo.csg_index_exists('DetailedInteractionSegEndDate', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteractionSegEndDate] ON [detailedInteractionData] ([segmentenddate]);
IF dbo.csg_index_exists('DetailedInteraction-StartDteLte', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteraction-StartDteLte] ON [detailedInteractionData] ([conversationstartdateltc]);
IF dbo.csg_index_exists('DetailedInteractionUserDirection', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteractionUserDirection] ON [detailedInteractionData] ([userid], [sessiondirection]);
IF dbo.csg_index_exists('DetailedInteractionConvEndLTC', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteractionConvEndLTC] ON [detailedInteractionData] ([conversationenddateltc]);
IF dbo.csg_index_exists('DetailedInteractionConvEnd', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteractionConvEnd] ON [detailedInteractionData] ([conversationenddate]);
IF dbo.csg_index_exists('DetailedInteractionSegEndLTC', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteractionSegEndLTC] ON [detailedInteractionData] ([segmentenddateltc]);
IF dbo.csg_index_exists('DetailedInteractionSegStart', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteractionSegStart] ON [detailedInteractionData] ([segmentstartdate]);
IF dbo.csg_index_exists('DetailedInteractionSegStartLTC', 'detailedInteractionData') = 0
CREATE INDEX [DetailedInteractionSegStartLTC] ON [detailedInteractionData] ([segmentstartdateltc]);

DROP VIEW IF EXISTS vwDetailedInteractionData;
DROP VIEW IF EXISTS vwCallAbandonedSummary ;
DROP VIEW IF EXISTS vwCallNotRespondingDetails;

IF dbo.csg_column_exists('detailedInteractionData', 'externalTag') = 0
    ALTER TABLE detailedInteractionData ADD externalTag NVARCHAR(50);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN externalTag NVARCHAR(50);

IF dbo.csg_column_exists('detailedInteractionData', 'peer') = 0
    ALTER TABLE detailedInteractionData ADD peer NVARCHAR(50);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN peer NVARCHAR(50);

IF dbo.csg_column_exists('detailedInteractionData', 'tconnected') = 0
    ALTER TABLE detailedInteractionData ADD tconnected DECIMAL(20, 2);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN tconnected DECIMAL(20, 2);

IF dbo.csg_column_exists('detailedInteractionData', 'tfirstconnect') = 0
    ALTER TABLE detailedInteractionData ADD tfirstconnect DECIMAL(20, 2);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN tfirstconnect DECIMAL(20, 2);

IF dbo.csg_column_exists('detailedInteractionData', 'tfirstdial') = 0
    ALTER TABLE detailedInteractionData ADD tfirstdial DECIMAL(20, 2);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN tfirstdial DECIMAL(20, 2);

IF dbo.csg_column_exists('detailedInteractionData', 'tmonitoringcomplete') = 0
    ALTER TABLE detailedInteractionData ADD tmonitoringcomplete DECIMAL(20, 2);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN tmonitoringcomplete DECIMAL(20, 2);

IF dbo.csg_column_exists('detailedInteractionData', 'nflowoutcome') = 0
    ALTER TABLE detailedInteractionData ADD nflowoutcome INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN nflowoutcome INT;

IF dbo.csg_column_exists('detailedInteractionData', 'tflowoutcome') = 0
    ALTER TABLE detailedInteractionData ADD tflowoutcome DECIMAL(20, 2);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN tflowoutcome DECIMAL(20, 2);

IF dbo.csg_column_exists('detailedInteractionData', 'nflowoutcomeFailed') = 0
    ALTER TABLE detailedInteractionData ADD nflowoutcomeFailed INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN nflowoutcomeFailed INT;

IF dbo.csg_column_exists('detailedInteractionData', 'noutboundattempted') = 0
    ALTER TABLE detailedInteractionData ADD noutboundattempted INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN noutboundattempted INT;

IF dbo.csg_column_exists('detailedInteractionData', 'noutboundconnected') = 0
    ALTER TABLE detailedInteractionData ADD noutboundconnected INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN noutboundconnected INT;
IF dbo.csg_column_exists('detailedInteractionData', 'nbotinteractions') = 0
    ALTER TABLE detailedInteractionData ADD nbotinteractions INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN nbotinteractions INT;
IF dbo.csg_column_exists('detailedInteractionData', 'tuserresponsetime') = 0
    ALTER TABLE detailedInteractionData ADD tuserresponsetime DECIMAL(20, 2);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN tuserresponsetime DECIMAL(20, 2);
IF dbo.csg_column_exists('detailedInteractionData', 'tActiveCallback') = 0
    ALTER TABLE detailedInteractionData ADD tActiveCallback DECIMAL(20, 2);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN tActiveCallback DECIMAL(20, 2);
IF dbo.csg_column_exists('detailedInteractionData', 'tActiveCallbackComplete') = 0
    ALTER TABLE detailedInteractionData ADD tActiveCallbackComplete DECIMAL(20, 2);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN tActiveCallbackComplete DECIMAL(20, 2);
IF dbo.csg_column_exists('detailedInteractionData', 'tmonitoringcomplete') = 0
    ALTER TABLE detailedInteractionData ADD tmonitoringcomplete DECIMAL(20, 2);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN tmonitoringcomplete DECIMAL(20, 2);
IF dbo.csg_column_exists('detailedInteractionData', 'wrapupnote') = 0
    ALTER TABLE detailedInteractionData ADD wrapupnote TEXT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN wrapupnote TEXT;
IF dbo.csg_column_exists('detailedInteractionData', 'segmenttime') = 0
    ALTER TABLE detailedInteractionData ADD segmenttime NUMERIC(20, 2);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN segmenttime NUMERIC(20, 2);
IF dbo.csg_column_exists('detailedInteractionData', 'convtosegmentstarttime') = 0
    ALTER TABLE detailedInteractionData ADD convtosegmentstarttime NUMERIC(20, 2);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN convtosegmentstarttime NUMERIC(20, 2);
IF dbo.csg_column_exists('detailedInteractionData', 'convtosegmentendtime') = 0
    ALTER TABLE detailedInteractionData ADD convtosegmentendtime NUMERIC(20, 2);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN convtosegmentendtime NUMERIC(20, 2);
IF dbo.csg_column_exists('detailedInteractionData', 'tPark') = 0
    ALTER TABLE detailedInteractionData ADD tPark DECIMAL(20, 2);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN tPark DECIMAL(20, 2);
IF dbo.csg_column_exists('detailedInteractionData', 'tParkComplete') = 0
    ALTER TABLE detailedInteractionData ADD tParkComplete DECIMAL(20, 2);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN tParkComplete DECIMAL(20, 2);
IF dbo.csg_column_exists('detailedInteractionData', 'nCobrowseSessions') = 0
    ALTER TABLE detailedInteractionData ADD nCobrowseSessions INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN nCobrowseSessions INT;

IF dbo.csg_column_exists('detailedInteractionData', 'nOutboundAbandoned') = 0
    ALTER TABLE detailedInteractionData ADD nOutboundAbandoned INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN nOutboundAbandoned INT;

IF dbo.csg_column_exists('detailedInteractionData', 'nStateTransitionError') = 0
    ALTER TABLE detailedInteractionData ADD nStateTransitionError INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN nStateTransitionError INT;

IF dbo.csg_column_exists('detailedInteractionData', 'oMessageCount') = 0
    ALTER TABLE detailedInteractionData ADD oMessageCount INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN oMessageCount INT;

IF dbo.csg_column_exists('detailedInteractionData', 'oMessageSegmentCount') = 0
    ALTER TABLE detailedInteractionData ADD oMessageSegmentCount INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN oMessageSegmentCount INT;

-- Add missing Session-level fields
IF dbo.csg_column_exists('detailedInteractionData', 'sessionid') = 0
    ALTER TABLE detailedInteractionData ADD sessionid NVARCHAR(50);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN sessionid NVARCHAR(50);

IF dbo.csg_column_exists('detailedInteractionData', 'protocolcallid') = 0
    ALTER TABLE detailedInteractionData ADD protocolcallid NVARCHAR(100);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN protocolcallid NVARCHAR(100);

IF dbo.csg_column_exists('detailedInteractionData', 'remotenamedisplayable') = 0
    ALTER TABLE detailedInteractionData ADD remotenamedisplayable NVARCHAR(255);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN remotenamedisplayable NVARCHAR(255);

IF dbo.csg_column_exists('detailedInteractionData', 'callbackusername') = 0
    ALTER TABLE detailedInteractionData ADD callbackusername NVARCHAR(255);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN callbackusername NVARCHAR(255);

IF dbo.csg_column_exists('detailedInteractionData', 'callbacknumbers') = 0
    ALTER TABLE detailedInteractionData ADD callbacknumbers NTEXT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN callbacknumbers NTEXT;

IF dbo.csg_column_exists('detailedInteractionData', 'scriptid') = 0
    ALTER TABLE detailedInteractionData ADD scriptid NVARCHAR(50);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN scriptid NVARCHAR(50);

IF dbo.csg_column_exists('detailedInteractionData', 'skipenabled') = 0
    ALTER TABLE detailedInteractionData ADD skipenabled BIT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN skipenabled BIT;

IF dbo.csg_column_exists('detailedInteractionData', 'timeoutseconds') = 0
    ALTER TABLE detailedInteractionData ADD timeoutseconds INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN timeoutseconds INT;

IF dbo.csg_column_exists('detailedInteractionData', 'flowouttype') = 0
    ALTER TABLE detailedInteractionData ADD flowouttype NVARCHAR(50);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN flowouttype NVARCHAR(50);

IF dbo.csg_column_exists('detailedInteractionData', 'roomid') = 0
    ALTER TABLE detailedInteractionData ADD roomid NVARCHAR(50);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN roomid NVARCHAR(50);

IF dbo.csg_column_exists('detailedInteractionData', 'callbackscheduledtime') = 0
    ALTER TABLE detailedInteractionData ADD callbackscheduledtime DATETIME;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN callbackscheduledtime DATETIME;

-- Add missing Flow-level fields
IF dbo.csg_column_exists('detailedInteractionData', 'transfertargetaddress') = 0
    ALTER TABLE detailedInteractionData ADD transfertargetaddress NVARCHAR(255);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN transfertargetaddress NVARCHAR(255);

IF dbo.csg_column_exists('detailedInteractionData', 'startinglanguage') = 0
    ALTER TABLE detailedInteractionData ADD startinglanguage NVARCHAR(50);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN startinglanguage NVARCHAR(50);

IF dbo.csg_column_exists('detailedInteractionData', 'endinglanguage') = 0
    ALTER TABLE detailedInteractionData ADD endinglanguage NVARCHAR(50);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN endinglanguage NVARCHAR(50);

-- Add missing Segment-level fields
IF dbo.csg_column_exists('detailedInteractionData', 'requestedroutingskillids') = 0
    ALTER TABLE detailedInteractionData ADD requestedroutingskillids NTEXT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN requestedroutingskillids NTEXT;

IF dbo.csg_column_exists('detailedInteractionData', 'sipresponsecodes') = 0
    ALTER TABLE detailedInteractionData ADD sipresponsecodes NTEXT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN sipresponsecodes NTEXT;

IF dbo.csg_column_exists('detailedInteractionData', 'q850responsecodes') = 0
    ALTER TABLE detailedInteractionData ADD q850responsecodes NTEXT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN q850responsecodes NTEXT;

IF dbo.csg_column_exists('detailedInteractionData', 'errorcode') = 0
    ALTER TABLE detailedInteractionData ADD errorcode NVARCHAR(100);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN errorcode NVARCHAR(100);

IF dbo.csg_column_exists('detailedInteractionData', 'requestedlanguageid') = 0
    ALTER TABLE detailedInteractionData ADD requestedlanguageid NVARCHAR(50);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN requestedlanguageid NVARCHAR(50);

-- Add missing Participant-level fields
IF dbo.csg_column_exists('detailedInteractionData', 'externalcontactid') = 0
    ALTER TABLE detailedInteractionData ADD externalcontactid NVARCHAR(50);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN externalcontactid NVARCHAR(50);

IF dbo.csg_column_exists('detailedInteractionData', 'externalorganizationid') = 0
    ALTER TABLE detailedInteractionData ADD externalorganizationid NVARCHAR(50);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN externalorganizationid NVARCHAR(50);

-- Add missing Media Endpoint Statistics fields
IF dbo.csg_column_exists('detailedInteractionData', 'codecs') = 0
    ALTER TABLE detailedInteractionData ADD codecs NTEXT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN codecs NTEXT;

IF dbo.csg_column_exists('detailedInteractionData', 'minmos') = 0
    ALTER TABLE detailedInteractionData ADD minmos DECIMAL(10, 4);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN minmos DECIMAL(10, 4);

IF dbo.csg_column_exists('detailedInteractionData', 'minrfactor') = 0
    ALTER TABLE detailedInteractionData ADD minrfactor DECIMAL(10, 4);
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN minrfactor DECIMAL(10, 4);

IF dbo.csg_column_exists('detailedInteractionData', 'maxlatencyms') = 0
    ALTER TABLE detailedInteractionData ADD maxlatencyms INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN maxlatencyms INT;

IF dbo.csg_column_exists('detailedInteractionData', 'receivedpackets') = 0
    ALTER TABLE detailedInteractionData ADD receivedpackets INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN receivedpackets INT;

IF dbo.csg_column_exists('detailedInteractionData', 'discardedpackets') = 0
    ALTER TABLE detailedInteractionData ADD discardedpackets INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN discardedpackets INT;

IF dbo.csg_column_exists('detailedInteractionData', 'overrunpackets') = 0
    ALTER TABLE detailedInteractionData ADD overrunpackets INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN overrunpackets INT;

IF dbo.csg_column_exists('detailedInteractionData', 'invalidpackets') = 0
    ALTER TABLE detailedInteractionData ADD invalidpackets INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN invalidpackets INT;

IF dbo.csg_column_exists('detailedInteractionData', 'duplicatepackets') = 0
    ALTER TABLE detailedInteractionData ADD duplicatepackets INT;
ELSE
    ALTER TABLE detailedInteractionData ALTER COLUMN duplicatepackets INT;

