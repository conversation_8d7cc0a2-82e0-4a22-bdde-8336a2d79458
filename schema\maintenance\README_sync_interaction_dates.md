# Interaction Table Sync Date Synchronization Scripts

## Overview

These SQL scripts are designed to synchronize the `maxdate` values in the `tabledefinitions` table for all interaction job tables. This ensures consistent synchronization dates across all interaction-related tables to prevent data gaps after the implementation of individual table sync date tracking.

## Background

The Genesys Adapter recently implemented individual table sync date tracking for the interaction job, where each table (`detailedinteractiondata`, `convsummarydata`, `participantattributesdynamic`, `participantsummarydata`, `flowoutcomedata`) maintains its own sync date in the `tabledefinitions` table. This enhancement prevents data gaps by ensuring comprehensive data synchronization.

However, existing installations may have inconsistent sync dates between these tables, which could lead to:
- Data gaps in some tables
- Incomplete interaction data
- Synchronization issues during job execution

## Purpose

These scripts address the synchronization issue by:
1. Using the sync date from `detailedinteractiondata` as the authoritative source
2. Setting all other interaction tables to use this authoritative date
3. Ensuring consistent synchronization across all interaction-related tables

## Files Included

- `postgres/maintenance/sync_interaction_table_dates.sql` - PostgreSQL version
- `mssql/maintenance/sync_interaction_table_dates.sql` - MSSQL version  
- `snowflake/maintenance/sync_interaction_table_dates.sql` - Snowflake version

## Tables Affected

The scripts synchronize dates for the following tables (excluding backfill tables):
- `detailedinteractiondata` (used as authoritative source)
- `convsummarydata`
- `participantattributesdynamic`
- `participantsummarydata`
- `flowoutcomedata`

**Note:** Backfill tables (`*_backfill`) are intentionally excluded from synchronization.

## Usage Instructions

### Before Running
1. **Backup your database** - Always create a backup before running maintenance scripts
2. **Stop the Genesys Adapter** - Ensure no jobs are running during synchronization
3. **Verify table names** - Confirm the interaction tables exist in your database

### Running the Scripts

#### PostgreSQL
```sql
-- Connect to your PostgreSQL database
\i schema/postgres/maintenance/sync_interaction_table_dates.sql
```

#### MSSQL
```sql
-- Connect to your MSSQL database using SQL Server Management Studio or sqlcmd
-- Execute the script: schema/mssql/maintenance/sync_interaction_table_dates.sql
```

#### Snowflake
```sql
-- Connect to your Snowflake database
-- Execute the script: schema/snowflake/maintenance/sync_interaction_table_dates.sql
```

### After Running
1. **Verify results** - Check the output to confirm all tables have the same sync date
2. **Restart the Genesys Adapter** - Resume normal operations
3. **Monitor the next interaction job run** - Ensure it processes data correctly

## Script Behavior

### Default Behavior
- **Version Check**: Only processes tables with version = '3.47.4'
- **Authoritative Source**: Uses the sync date from `detailedinteractiondata` as the reference
- **Conditional Updates**: Only updates tables where current maxdate is older than the authoritative date
- **Backfill Exclusion**: Excludes all `*_backfill` tables from synchronization
- **Verification**: Displays the reference date and synchronized dates for confirmation

### Fallback Behavior
- If `detailedinteractiondata` has a NULL or empty sync date, it defaults to `2000-01-01 00:00:00`
- If no `detailedinteractiondata` record exists with version '3.47.4', the script exits safely
- This ensures a safe starting point for data synchronization

### Optional Custom Date
Each script includes commented code to set a specific sync date if needed:
```sql
-- First update detailedinteractiondata, then sync others
-- UPDATE tabledefinitions
-- SET datekeyfield = '2024-01-01 00:00:00'
-- WHERE tablename = 'detailedinteractiondata';
-- (then update other tables to match)
```

## Expected Output

The scripts will display:
1. The authoritative sync date from `detailedinteractiondata`
2. A table showing all interaction tables with their synchronized dates
3. Confirmation message (MSSQL only)

Example output:
```
reference_table              | reference_date           | description
-----------------------------|--------------------------|------------------------------------------
detailedinteractiondata      | 2024-06-15 10:30:00     | This date was used for all interaction tables

tablename                    | synchronized_date        | version | status
-----------------------------|--------------------------|---------|--------------------------------------------------
detailedinteractiondata      | 2024-06-15 10:30:00     | 3.47.4  | Interaction table sync dates synchronized to detailedinteractiondata
convsummarydata              | 2024-06-15 10:30:00     | 3.47.4  | Interaction table sync dates synchronized to detailedinteractiondata
participantattributesdynamic | 2024-06-15 10:30:00     | 3.47.4  | Interaction table sync dates synchronized to detailedinteractiondata
participantsummarydata       | 2024-06-15 10:30:00     | 3.47.4  | Interaction table sync dates synchronized to detailedinteractiondata
flowoutcomedata              | 2024-06-15 10:30:00     | 3.47.4  | Interaction table sync dates synchronized to detailedinteractiondata
```

## Troubleshooting

### Common Issues
1. **Table not found** - Verify the interaction tables exist in your database
2. **Permission denied** - Ensure you have UPDATE permissions on the `tabledefinitions` table
3. **Date format errors** - Check that existing dates in `datekeyfield` are in the correct format

### Verification Queries
After running the script, you can verify the results:

```sql
-- Check all interaction table sync dates
SELECT tablename, datekeyfield 
FROM tabledefinitions 
WHERE tablename LIKE '%interaction%' 
   OR tablename LIKE '%convsummary%' 
   OR tablename LIKE '%participant%' 
   OR tablename LIKE '%flowoutcome%'
ORDER BY tablename;
```

## Support

If you encounter issues with these scripts:
1. Check the Genesys Adapter logs for any related errors
2. Verify your database permissions
3. Ensure the `tabledefinitions` table structure matches expectations
4. Contact support with the specific error message and database type
