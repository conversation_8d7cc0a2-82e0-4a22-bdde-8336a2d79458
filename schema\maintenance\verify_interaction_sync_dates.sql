-- Universal verification script for interaction table sync dates
-- This script works across PostgreSQL, MSSQL, and Snowflake to verify synchronization
-- Run this after executing the sync scripts to confirm all tables match detailedinteractiondata

-- Display all interaction table sync dates (excluding backfill tables)
SELECT
    tablename,
    datekeyfield as sync_date,
    version,
    CASE
        WHEN datekeyfield IS NULL OR datekeyfield = '' THEN 'NULL/EMPTY'
        ELSE 'HAS_DATE'
    END as date_status
FROM tabledefinitions
WHERE tablename IN (
    'detailedinteractiondata',
    'convsummarydata',
    'participantattributesdynamic',
    'participantsummarydata',
    'flowoutcomedata'
)
AND version = '3.47.4'
ORDER BY tablename;

-- Get the reference date from detailedinteractiondata
SELECT
    'detailedinteractiondata' as reference_table,
    datekeyfield as reference_date,
    version,
    'This should match all other interaction tables (v3.47.4)' as description
FROM tabledefinitions
WHERE tablename = 'detailedinteractiondata'
    AND version = '3.47.4';

-- Summary check - should return only one row if all dates are synchronized to detailedinteractiondata
SELECT
    COUNT(DISTINCT datekeyfield) as unique_sync_dates,
    CASE
        WHEN COUNT(DISTINCT datekeyfield) = 1 THEN 'SYNCHRONIZED_TO_DETAILEDINTERACTIONDATA'
        WHEN COUNT(DISTINCT datekeyfield) > 1 THEN 'NOT_SYNCHRONIZED'
        ELSE 'ERROR'
    END as synchronization_status,
    MIN(datekeyfield) as earliest_date,
    MAX(datekeyfield) as latest_date
FROM tabledefinitions
WHERE tablename IN (
    'detailedinteractiondata',
    'convsummarydata',
    'participantattributesdynamic',
    'participantsummarydata',
    'flowoutcomedata'
)
AND version = '3.47.4';

-- Check for any inconsistent dates (grouped by sync date)
SELECT
    datekeyfield as sync_date,
    COUNT(*) as table_count
FROM tabledefinitions
WHERE tablename IN (
    'detailedinteractiondata',
    'convsummarydata',
    'participantattributesdynamic',
    'participantsummarydata',
    'flowoutcomedata'
)
AND version = '3.47.4'
GROUP BY datekeyfield
ORDER BY sync_date;
